import { useEffect } from 'react';
import styled from 'styled-components';
import { useAnalytics } from '../hooks/useAnalytics';
import { useLanguage } from '../hooks/useLanguage';

const PitchContainer = styled.div`
  min-height: 100vh;
  padding: var(--spacing-12) 0;
`;

// Using global .container class from global.css

const PitchContent = styled.div`
  max-width: 1000px;
  margin: 0 auto;
`;

const HeroSection = styled.section`
  text-align: center;
  margin-bottom: var(--spacing-16);

  h1 {
    font-size: var(--font-size-4xl);
    margin-bottom: var(--spacing-6);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .subtitle {
    font-size: var(--font-size-xl);
    color: var(--gray-600);
    margin-bottom: var(--spacing-8);
  }
`;

const StrategySection = styled.section`
  margin-bottom: var(--spacing-16);
`;

const StrategyGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-8);
  margin-bottom: var(--spacing-12);
`;

const StrategyCard = styled.div`
  background: var(--white);
  border-radius: var(--radius-xl);
  padding: var(--spacing-8);
  box-shadow: var(--shadow-lg);
  border: 2px solid transparent;
  transition: all var(--transition-fast);

  &:hover {
    border-color: var(--primary-blue);
    transform: translateY(-4px);
  }

  .icon {
    font-size: 3rem;
    margin-bottom: var(--spacing-4);
  }

  h3 {
    margin-bottom: var(--spacing-4);
    color: var(--gray-900);
  }

  p {
    color: var(--gray-600);
    line-height: 1.6;
  }

  ul {
    margin-top: var(--spacing-4);
    padding-left: var(--spacing-4);

    li {
      margin-bottom: var(--spacing-2);
      color: var(--gray-600);
    }
  }
`;

const CTASection = styled.section`
  background: var(--gradient-primary);
  color: var(--white);
  padding: var(--spacing-12);
  border-radius: var(--radius-xl);
  text-align: center;
  margin-top: var(--spacing-16);

  h2 {
    color: var(--white);
    margin-bottom: var(--spacing-4);
  }

  p {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-6);
    opacity: 0.9;
  }
`;

const CTAButton = styled.a`
  display: inline-block;
  background: var(--white);
  color: var(--primary-blue);
  padding: var(--spacing-4) var(--spacing-8);
  border-radius: var(--radius-xl);
  font-size: var(--font-size-lg);
  font-weight: 600;
  text-decoration: none;
  box-shadow: var(--shadow-lg);
  transition: all var(--transition-fast);
  margin: var(--spacing-2);

  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
    color: var(--primary-blue);
    text-decoration: none;
  }

  &.secondary {
    background: transparent;
    color: var(--primary-blue);
    border: 2px solid var(--primary-blue);

    &:hover {
      background: var(--primary-blue);
      color: var(--white);
    }
  }
`;

const CTAButtons = styled.div`
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: var(--spacing-2);

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: center;
  }
`;

const PitchPage = () => {
  const { trackPageView, trackButtonClick } = useAnalytics();
  const { t } = useLanguage();

  useEffect(() => {
    trackPageView('/pitch');
  }, [trackPageView]);



  const primaryAffiliateUrl = import.meta.env.VITE_PRIMARY_AFFILIATE_URL;

  return (
    <PitchContainer>
      <div className="container">
        <PitchContent>
        <HeroSection>
          <h1>{t('strategy.title')}</h1>
          <p className="subtitle">
            {t('strategy.subtitle')}
          </p>
        </HeroSection>

        <StrategySection>
          <StrategyGrid>
            <StrategyCard>
              <div className="icon">📈</div>
              <h3>{t('strategy.cards.trendFollowing.title')}</h3>
              <p>
                {t('strategy.cards.trendFollowing.description')}
              </p>
              <ul>
                {t('strategy.cards.trendFollowing.points').map((point, index) => (
                  <li key={index}>{point}</li>
                ))}
              </ul>
            </StrategyCard>

            <StrategyCard>
              <div className="icon">🎯</div>
              <h3>{t('strategy.cards.marketStructure.title')}</h3>
              <p>
                {t('strategy.cards.marketStructure.description')}
              </p>
              <ul>
                {t('strategy.cards.marketStructure.points').map((point, index) => (
                  <li key={index}>{point}</li>
                ))}
              </ul>
            </StrategyCard>

            <StrategyCard>
              <div className="icon">📊</div>
              <h3>{t('strategy.cards.priceLevel.title')}</h3>
              <p>
                {t('strategy.cards.priceLevel.description')}
              </p>
              <ul>
                {t('strategy.cards.priceLevel.points').map((point, index) => (
                  <li key={index}>{point}</li>
                ))}
              </ul>
            </StrategyCard>

            <StrategyCard>
              <div className="icon">⚡</div>
              <h3>{t('strategy.cards.momentum.title')}</h3>
              <p>
                {t('strategy.cards.momentum.description')}
              </p>
              <ul>
                {t('strategy.cards.momentum.points').map((point, index) => (
                  <li key={index}>{point}</li>
                ))}
              </ul>
            </StrategyCard>

            <StrategyCard>
              <div className="icon">🛡️</div>
              <h3>{t('strategy.cards.moneyManagement.title')}</h3>
              <p>
                {t('strategy.cards.moneyManagement.description')}
              </p>
              <ul>
                {t('strategy.cards.moneyManagement.points').map((point, index) => (
                  <li key={index}>{point}</li>
                ))}
              </ul>
            </StrategyCard>

            <StrategyCard>
              <div className="icon">🎓</div>
              <h3>{t('strategy.cards.psychology.title')}</h3>
              <p>
                {t('strategy.cards.psychology.description')}
              </p>
              <ul>
                {t('strategy.cards.psychology.points').map((point, index) => (
                  <li key={index}>{point}</li>
                ))}
              </ul>
            </StrategyCard>
          </StrategyGrid>
        </StrategySection>

        <CTASection>
          <h2>{t('strategy.ctaTitle')}</h2>
          <p>
            {t('strategy.ctaDescription')}
          </p>
          <CTAButton
            href={primaryAffiliateUrl}
            target="_blank"
            rel="noopener noreferrer"
            onClick={() => trackButtonClick('cta_pitch', '/pitch')}
          >
            {t('strategy.cta')}
          </CTAButton>
        </CTASection>
        </PitchContent>
      </div>
    </PitchContainer>
  );
};

export default PitchPage;
