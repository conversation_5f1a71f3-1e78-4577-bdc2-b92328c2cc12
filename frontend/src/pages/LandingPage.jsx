import React, { useEffect } from 'react';
import styled from 'styled-components';
import { useAnalytics } from '../hooks/useAnalytics';
import { useLanguage } from '../hooks/useLanguage';

const LandingContainer = styled.div`
  min-height: 100vh;
`;

const HeroSection = styled.section`
  background: var(--gradient-primary);
  color: var(--white);
  padding: var(--spacing-20) 0;
  text-align: center;
`;

// Using global .container class from global.css

const HeroContent = styled.div`
  max-width: 800px;
  margin: 0 auto;

  h1 {
    font-size: var(--font-size-4xl);
    margin-bottom: var(--spacing-6);
    color: var(--white);
  }

  p {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-8);
    opacity: 0.9;
  }

  @media (max-width: 768px) {
    h1 {
      font-size: var(--font-size-3xl);
    }

    p {
      font-size: var(--font-size-lg);
    }
  }
`;

const CTAButton = styled.a`
  display: inline-block;
  background: var(--white);
  color: var(--primary-blue);
  padding: var(--spacing-4) var(--spacing-8);
  border-radius: var(--radius-xl);
  font-size: var(--font-size-lg);
  font-weight: 600;
  text-decoration: none;
  box-shadow: var(--shadow-lg);
  transition: all var(--transition-fast);
  margin: var(--spacing-2);

  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
    color: var(--primary-blue);
    text-decoration: none;
  }

  &.secondary {
    background: transparent;
    color: var(--primary-blue);
    border: 2px solid var(--primary-blue);

    &:hover {
      background: var(--primary-blue);
      color: var(--white);
    }
  }
`;

const CTAButtons = styled.div`
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: var(--spacing-2);

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: center;
  }
`;

const FeaturesSection = styled.section`
  padding: var(--spacing-20) 0;
  background: var(--gray-100);
`;

const FeaturesGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-8);
`;

const FeatureCard = styled.div`
  background: var(--white);
  padding: var(--spacing-6);
  border-radius: var(--radius-xl);
  text-align: center;
  box-shadow: var(--shadow-md);
  transition: transform var(--transition-fast);
  
  &:hover {
    transform: translateY(-4px);
  }
  
  .icon {
    font-size: 3rem;
    margin-bottom: var(--spacing-4);
  }
  
  h3 {
    margin-bottom: var(--spacing-3);
    color: var(--gray-900);
  }
  
  p {
    color: var(--gray-600);
  }
`;

const TestimonialSection = styled.section`
  padding: var(--spacing-20) 0;
  text-align: center;
  background: var(--gray-50);
`;

const TestimonialContainer = styled.div`
  max-width: 900px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);

  h2 {
    margin-bottom: var(--spacing-8);
    color: var(--gray-900);
  }
`;

const TestimonialCarousel = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-6);
  margin-bottom: var(--spacing-8);

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

const TestimonialCard = styled.div`
  background: var(--white);
  padding: var(--spacing-6);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  text-align: left;
  position: relative;

  &::before {
    content: '"';
    position: absolute;
    top: var(--spacing-4);
    left: var(--spacing-4);
    font-size: 3rem;
    color: var(--primary-blue);
    opacity: 0.3;
    font-family: serif;
  }
`;

const TestimonialHeader = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-4);
  margin-top: var(--spacing-2);
`;

const Avatar = styled.div`
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: var(--gradient-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  font-weight: bold;
  font-size: var(--font-size-lg);
  margin-right: var(--spacing-3);
`;

const UserInfo = styled.div`
  flex: 1;

  h4 {
    margin: 0 0 var(--spacing-1) 0;
    color: var(--gray-900);
    font-size: var(--font-size-base);
  }

  p {
    margin: 0;
    color: var(--gray-600);
    font-size: var(--font-size-sm);
  }
`;

const Stars = styled.div`
  color: #fbbf24;
  font-size: var(--font-size-sm);
  margin-left: auto;
`;

const TestimonialText = styled.p`
  color: var(--gray-700);
  font-style: italic;
  line-height: 1.6;
  margin-bottom: var(--spacing-3);
`;

const VerifiedBadge = styled.span`
  background: var(--success);
  color: var(--white);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-md);
  font-size: var(--font-size-xs);
  font-weight: 600;
`;

const LandingPage = () => {
  const { trackPageView, trackButtonClick } = useAnalytics();
  const { t } = useLanguage();

  useEffect(() => {
    trackPageView('/');
  }, [trackPageView]);

  const handleCTAClick = (location) => {
    trackButtonClick(`cta_${location}`, '/');
  };

  const primaryAffiliateUrl = import.meta.env.VITE_PRIMARY_AFFILIATE_URL;

  return (
    <LandingContainer>
      <HeroSection>
        <div className="container">
          <HeroContent>
            <h1>{t('landing.hero.title')}</h1>
            <p>
              {t('landing.hero.subtitle')}
            </p>
            <CTAButton
              href={primaryAffiliateUrl}
              target="_blank"
              rel="noopener noreferrer"
              onClick={() => handleCTAClick('hero')}
            >
              {t('landing.hero.cta')}
            </CTAButton>
          </HeroContent>
        </div>
      </HeroSection>

      <FeaturesSection>
        <div className="container">
          <FeaturesGrid>
          <FeatureCard>
            <div className="icon">📈</div>
            <h3>{t('landing.features.momentum.title')}</h3>
            <p>
              {t('landing.features.momentum.description')}
            </p>
          </FeatureCard>

          <FeatureCard>
            <div className="icon">🎯</div>
            <h3>{t('landing.features.priceLevel.title')}</h3>
            <p>
              {t('landing.features.priceLevel.description')}
            </p>
          </FeatureCard>

          <FeatureCard>
            <div className="icon">🛡️</div>
            <h3>{t('landing.features.riskManagement.title')}</h3>
            <p>
              {t('landing.features.riskManagement.description')}
            </p>
          </FeatureCard>
          </FeaturesGrid>
        </div>
      </FeaturesSection>

      <TestimonialSection>
        <div className="container">
          <TestimonialContainer>
            <h2>{t('landing.testimonials.title')}</h2>

            <TestimonialCarousel>
              <TestimonialCard>
                <TestimonialHeader>
                  <Avatar>VA</Avatar>
                  <UserInfo>
                    <h4>{t('landing.testimonials.users.vladimir.name')}</h4>
                    <p>{t('common.verifiedTrader')}</p>
                  </UserInfo>
                  <Stars>★★★★★</Stars>
                </TestimonialHeader>
                <TestimonialText>
                  "{t('landing.testimonials.users.vladimir.text')}"
                </TestimonialText>
                <VerifiedBadge>{t('landing.testimonials.verified')}</VerifiedBadge>
              </TestimonialCard>

              <TestimonialCard>
                <TestimonialHeader>
                  <Avatar>NH</Avatar>
                  <UserInfo>
                    <h4>{t('landing.testimonials.users.nicholas.name')}</h4>
                    <p>{t('common.verifiedTrader')}</p>
                  </UserInfo>
                  <Stars>★★★★★</Stars>
                </TestimonialHeader>
                <TestimonialText>
                  "{t('landing.testimonials.users.nicholas.text')}"
                </TestimonialText>
                <VerifiedBadge>{t('landing.testimonials.verified')}</VerifiedBadge>
              </TestimonialCard>

              <TestimonialCard>
                <TestimonialHeader>
                  <Avatar>MP</Avatar>
                  <UserInfo>
                    <h4>{t('landing.testimonials.users.miguel.name')}</h4>
                    <p>{t('common.verifiedTrader')}</p>
                  </UserInfo>
                  <Stars>★★★★☆</Stars>
                </TestimonialHeader>
                <TestimonialText>
                  "{t('landing.testimonials.users.miguel.text')}"
                </TestimonialText>
                <VerifiedBadge>{t('landing.testimonials.verified')}</VerifiedBadge>
              </TestimonialCard>

              <TestimonialCard>
                <TestimonialHeader>
                  <Avatar>IC</Avatar>
                  <UserInfo>
                    <h4>{t('landing.testimonials.users.inaki.name')}</h4>
                    <p>{t('common.verifiedTrader')}</p>
                  </UserInfo>
                  <Stars>★★★★★</Stars>
                </TestimonialHeader>
                <TestimonialText>
                  "{t('landing.testimonials.users.inaki.text')}"
                </TestimonialText>
                <VerifiedBadge>{t('landing.testimonials.verified')}</VerifiedBadge>
              </TestimonialCard>

              <TestimonialCard>
                <TestimonialHeader>
                  <Avatar>RM</Avatar>
                  <UserInfo>
                    <h4>{t('landing.testimonials.users.rosa.name')}</h4>
                    <p>{t('common.verifiedTrader')}</p>
                  </UserInfo>
                  <Stars>★★★★★</Stars>
                </TestimonialHeader>
                <TestimonialText>
                  "{t('landing.testimonials.users.rosa.text')}"
                </TestimonialText>
                <VerifiedBadge>{t('landing.testimonials.verified')}</VerifiedBadge>
              </TestimonialCard>

              <TestimonialCard>
                <TestimonialHeader>
                  <Avatar>TL</Avatar>
                  <UserInfo>
                    <h4>{t('landing.testimonials.users.thomas.name')}</h4>
                    <p>{t('common.verifiedTrader')}</p>
                  </UserInfo>
                  <Stars>★★★★☆</Stars>
                </TestimonialHeader>
                <TestimonialText>
                  "{t('landing.testimonials.users.thomas.text')}"
                </TestimonialText>
                <VerifiedBadge>{t('landing.testimonials.verified')}</VerifiedBadge>
              </TestimonialCard>
            </TestimonialCarousel>

            <CTAButton
              href={primaryAffiliateUrl}
              target="_blank"
              rel="noopener noreferrer"
              onClick={() => handleCTAClick('testimonial')}
            >
              {t('landing.testimonials.cta')}
            </CTAButton>
          </TestimonialContainer>
        </div>
      </TestimonialSection>
    </LandingContainer>
  );
};

export default LandingPage;
