import React from 'react';
import { Link } from 'react-router-dom';
import styled from 'styled-components';

const NotFoundContainer = styled.div`
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: var(--spacing-4);
`;

const NotFoundContent = styled.div`
  max-width: 500px;
  
  h1 {
    font-size: 6rem;
    margin-bottom: var(--spacing-4);
    color: var(--primary-blue);
  }
  
  h2 {
    margin-bottom: var(--spacing-4);
  }
  
  p {
    margin-bottom: var(--spacing-6);
    color: var(--gray-600);
  }
`;

const HomeButton = styled(Link)`
  display: inline-block;
  background: var(--gradient-primary);
  color: var(--white);
  padding: var(--spacing-3) var(--spacing-6);
  border-radius: var(--radius-lg);
  text-decoration: none;
  font-weight: 500;
  transition: all var(--transition-fast);
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
    color: var(--white);
    text-decoration: none;
  }
`;

const NotFound = () => {
  return (
    <NotFoundContainer>
      <NotFoundContent>
        <h1>404</h1>
        <h2>Page Not Found</h2>
        <p>
          The page you're looking for doesn't exist. 
          Let's get you back to copy trading!
        </p>
        <HomeButton to="/">
          Back to Home
        </HomeButton>
      </NotFoundContent>
    </NotFoundContainer>
  );
};

export default NotFound;
