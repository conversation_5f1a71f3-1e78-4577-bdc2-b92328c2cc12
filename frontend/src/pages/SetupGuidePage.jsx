import { useEffect } from 'react';
import styled from 'styled-components';
import { useAnalytics } from '../hooks/useAnalytics';
import { useLanguage } from '../hooks/useLanguage';

const GuideContainer = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
  padding: var(--spacing-4) 0;
`;

const GuideContent = styled.div`
  max-width: 800px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
`;

const GuideHeader = styled.div`
  text-align: center;
  margin-bottom: var(--spacing-8);
  color: white;

  h1 {
    font-size: var(--font-size-3xl);
    margin-bottom: var(--spacing-4);
    font-weight: 700;
  }

  p {
    font-size: var(--font-size-lg);
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
  }
`;

const GuideCard = styled.div`
  background: white;
  border-radius: var(--radius-xl);
  padding: var(--spacing-6);
  margin-bottom: var(--spacing-6);
  box-shadow: var(--shadow-lg);

  h2 {
    color: var(--primary);
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-4);
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
  }

  h3 {
    color: var(--gray-800);
    font-size: var(--font-size-lg);
    margin: var(--spacing-4) 0 var(--spacing-2) 0;
  }

  p {
    color: var(--gray-600);
    line-height: 1.6;
    margin-bottom: var(--spacing-3);
  }

  ul {
    color: var(--gray-600);
    padding-left: var(--spacing-4);
    
    li {
      margin-bottom: var(--spacing-2);
      line-height: 1.6;
    }
  }
`;

const ContactInfo = styled.div`
  background: var(--primary-light);
  border: 2px solid var(--primary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-4);
  margin: var(--spacing-4) 0;
  text-align: center;

  h3 {
    color: var(--primary);
    margin-bottom: var(--spacing-3);
  }

  .contact-methods {
    display: flex;
    justify-content: center;
    gap: var(--spacing-4);
    flex-wrap: wrap;
  }

  .contact-method {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    background: white;
    padding: var(--spacing-2) var(--spacing-3);
    border-radius: var(--radius-md);
    text-decoration: none;
    color: var(--primary);
    font-weight: 600;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-md);
    }
  }
`;

const WarningBox = styled.div`
  background: #fff3cd;
  border: 2px solid #ffc107;
  border-radius: var(--radius-lg);
  padding: var(--spacing-4);
  margin: var(--spacing-4) 0;

  h3 {
    color: #856404;
    margin-bottom: var(--spacing-2);
  }

  p {
    color: #856404;
    margin: 0;
  }
`;

const SetupGuidePage = () => {
  const { trackPageView } = useAnalytics();
  const { t } = useLanguage();

  useEffect(() => {
    trackPageView('/setup-guide');
  }, [trackPageView]);

  return (
    <GuideContainer>
      <GuideContent>
        <GuideHeader>
          <h1>{t('setupGuide.title')}</h1>
          <p>
            {t('setupGuide.subtitle')}
          </p>
        </GuideHeader>

        <GuideCard>
          <h2>{t('setupGuide.contactStep.title')}</h2>
          <p>
            {t('setupGuide.contactStep.description')}
          </p>

          <ContactInfo>
            <h3>{t('setupGuide.contactStep.contactMethods')}</h3>
            <div className="contact-methods">
              <a href="mailto:<EMAIL>" className="contact-method">
                {t('setupGuide.contactStep.email')}
              </a>
              <a href="https://t.me/secondstrikecopy" target="_blank" rel="noopener noreferrer" className="contact-method">
                {t('setupGuide.contactStep.telegram')}
              </a>
            </div>
          </ContactInfo>

          <WarningBox>
            <h3>{t('setupGuide.contactStep.important')}</h3>
            <p>
              {t('setupGuide.contactStep.warning')}
            </p>
          </WarningBox>
        </GuideCard>

        <GuideCard>
          <h2>{t('setupGuide.depositStep.title')}</h2>
          <p>
            {t('setupGuide.depositStep.description')}
          </p>

          <h3>{t('setupGuide.depositStep.whyCrypto')}</h3>
          <ul>
            <li><strong>{t('setupGuide.depositStep.cryptoBenefits.faster')}</strong></li>
            <li><strong>{t('setupGuide.depositStep.cryptoBenefits.lower')}</strong></li>
            <li><strong>{t('setupGuide.depositStep.cryptoBenefits.privacy')}</strong></li>
            <li><strong>{t('setupGuide.depositStep.cryptoBenefits.banking')}</strong></li>
          </ul>

          <h3>{t('setupGuide.depositStep.supported')}</h3>
          <ul>
            {t('setupGuide.depositStep.cryptoList').map((crypto, index) => (
              <li key={index}>{crypto}</li>
            ))}
          </ul>

          <p>
            <strong>{t('setupGuide.depositStep.minimum')}</strong>
          </p>
        </GuideCard>

        <GuideCard>
          <h2>{t('setupGuide.copyStep.title')}</h2>
          <p>
            {t('setupGuide.copyStep.description')}
          </p>

          <h3>{t('setupGuide.copyStep.helpWith')}</h3>
          <ul>
            {t('setupGuide.copyStep.helpList').map((item, index) => (
              <li key={index}>{item}</li>
            ))}
          </ul>

          <h3>{t('setupGuide.copyStep.options')}</h3>
          <ul>
            <li><strong>{t('setupGuide.copyStep.optionsList.small')}</strong></li>
            <li><strong>{t('setupGuide.copyStep.optionsList.medium')}</strong></li>
            <li><strong>{t('setupGuide.copyStep.optionsList.large')}</strong></li>
          </ul>
        </GuideCard>

        <GuideCard>
          <h2>{t('setupGuide.monitorStep.title')}</h2>
          <p>
            {t('setupGuide.monitorStep.description')}
          </p>

          <h3>{t('setupGuide.monitorStep.expect')}</h3>
          <ul>
            {t('setupGuide.monitorStep.expectList').map((item, index) => (
              <li key={index}>{item}</li>
            ))}
          </ul>

          <h3>{t('setupGuide.monitorStep.monitoring')}</h3>
          <ul>
            {t('setupGuide.monitorStep.monitoringList').map((item, index) => (
              <li key={index}>{item}</li>
            ))}
          </ul>
        </GuideCard>

        <GuideCard>
          <h2>{t('setupGuide.disclaimers.title')}</h2>
          <p>
            {t('setupGuide.disclaimers.description')}
          </p>

          <ul>
            {t('setupGuide.disclaimers.list').map((item, index) => (
              <li key={index}>{item}</li>
            ))}
          </ul>

          <ContactInfo>
            <h3>{t('setupGuide.contactStep.contactMethods')}</h3>
            <div className="contact-methods">
              <a href="mailto:<EMAIL>" className="contact-method">
                {t('setupGuide.contactStep.email')}
              </a>
              <a href="https://t.me/secondstrikecopy" target="_blank" rel="noopener noreferrer" className="contact-method">
                {t('setupGuide.contactStep.telegram')}
              </a>
            </div>
          </ContactInfo>
        </GuideCard>

        <div style={{ textAlign: 'center', marginTop: 'var(--spacing-8)' }}>
          <a
            href={import.meta.env.VITE_PRIMARY_AFFILIATE_URL}
            target="_blank"
            rel="noopener noreferrer"
            style={{
              display: 'inline-block',
              background: 'var(--primary-blue)',
              color: 'var(--white)',
              padding: 'var(--spacing-4) var(--spacing-8)',
              borderRadius: 'var(--radius-xl)',
              fontSize: 'var(--font-size-lg)',
              fontWeight: '600',
              textDecoration: 'none',
              boxShadow: 'var(--shadow-lg)',
              transition: 'all var(--transition-fast)'
            }}
          >
            {t('setupGuide.cta')}
          </a>
        </div>
      </GuideContent>
    </GuideContainer>
  );
};

export default SetupGuidePage;
