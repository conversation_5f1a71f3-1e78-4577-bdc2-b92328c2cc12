import React, { useEffect } from 'react';
import styled from 'styled-components';
import { useAnalytics } from '../hooks/useAnalytics';
import { useLanguage } from '../hooks/useLanguage';
import image1 from '../assets/image1.png';
import Image2 from '../assets/Image2.png';
import Image3 from '../assets/Image3.png';
import Image4 from '../assets/Image4.png';

const ProofContainer = styled.div`
  min-height: 100vh;
  padding: var(--spacing-12) 0;
`;

// Using global .container class from global.css

const ProofContent = styled.div`
  /* Content styling only, no positioning */
`;

const ProofHeader = styled.div`
  text-align: center;
  margin-bottom: var(--spacing-12);
  
  h1 {
    margin-bottom: var(--spacing-4);
  }
  
  p {
    font-size: var(--font-size-lg);
    color: var(--gray-600);
    max-width: 600px;
    margin: 0 auto;
  }
`;

const ProofGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--spacing-8);
  margin-bottom: var(--spacing-12);
`;

const ProofCard = styled.div`
  background: var(--white);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  
  .proof-image {
    background: var(--gray-200);
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--gray-500);
    font-size: var(--font-size-lg);
  }
  
  .proof-content {
    padding: var(--spacing-6);
    
    h3 {
      margin-bottom: var(--spacing-3);
    }
    
    p {
      color: var(--gray-600);
      margin-bottom: var(--spacing-2);
    }
    
    .stats {
      display: flex;
      justify-content: space-between;
      margin-top: var(--spacing-4);
      padding-top: var(--spacing-4);
      border-top: 1px solid var(--gray-200);
      
      .stat {
        text-align: center;
        
        .value {
          font-size: var(--font-size-lg);
          font-weight: 600;
          color: var(--success);
        }
        
        .label {
          font-size: var(--font-size-sm);
          color: var(--gray-500);
        }
      }
    }
  }
`;

const CTASection = styled.section`
  background: var(--gradient-primary);
  color: var(--white);
  padding: var(--spacing-12);
  border-radius: var(--radius-xl);
  text-align: center;
  
  h2 {
    color: var(--white);
    margin-bottom: var(--spacing-4);
  }
  
  p {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-6);
    opacity: 0.9;
  }
`;

const CTAButton = styled.a`
  display: inline-block;
  background: var(--white);
  color: var(--primary-blue);
  padding: var(--spacing-4) var(--spacing-8);
  border-radius: var(--radius-xl);
  font-size: var(--font-size-lg);
  font-weight: 600;
  text-decoration: none;
  box-shadow: var(--shadow-lg);
  transition: all var(--transition-fast);
  margin: var(--spacing-2);

  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
    color: var(--primary-blue);
    text-decoration: none;
  }

  &.secondary {
    background: transparent;
    color: var(--primary-blue);
    border: 2px solid var(--primary-blue);

    &:hover {
      background: var(--primary-blue);
      color: var(--white);
    }
  }
`;

const CTAButtons = styled.div`
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: var(--spacing-2);

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: center;
  }
`;

const ProofPage = () => {
  const { trackPageView, trackButtonClick } = useAnalytics();
  const { t } = useLanguage();

  useEffect(() => {
    trackPageView('/proof');
  }, [trackPageView]);



  const primaryAffiliateUrl = import.meta.env.VITE_PRIMARY_AFFILIATE_URL;

  return (
    <ProofContainer>
      <div className="container">
        <ProofContent>
        <ProofHeader>
          <h1>{t('proof.title')}</h1>
          <p>
            {t('proof.subtitle')}
          </p>
        </ProofHeader>

        <ProofGrid>


          <ProofCard>
            <div className="proof-image">
              <img src={Image2} alt="Trading Chart Analysis" style={{width: '100%', height: '100%', objectFit: 'cover'}} />
            </div>
            <div className="proof-content">
              <h3>{t('proof.cards.trendFollowing.title')}</h3>
              <p>{t('proof.cards.trendFollowing.description')}</p>
              <div className="stats">
                <div className="stat">
                  <div className="value">15M</div>
                  <div className="label">{t('proof.cards.trendFollowing.stats.analysisTF')}</div>
                </div>
                <div className="stat">
                  <div className="value">1M</div>
                  <div className="label">{t('proof.cards.trendFollowing.stats.entryTF')}</div>
                </div>
                <div className="stat">
                  <div className="value">High</div>
                  <div className="label">{t('proof.cards.trendFollowing.stats.probability')}</div>
                </div>
              </div>
            </div>
          </ProofCard>

          <ProofCard>
            <div className="proof-image">
              <img src={image1} alt="Trading Performance Results" style={{width: '90%', height: '90%', objectFit: 'cover'}} />
            </div>
            <div className="proof-content">
              <h3>{t('proof.cards.tradingResults.title')}</h3>
              <p>{t('proof.cards.tradingResults.description')}</p>
              <div className="stats">
                <div className="stat">
                  <div className="value">Profitable</div>
                  <div className="label">{t('proof.cards.tradingResults.stats.outcome')}</div>
                </div>
                <div className="stat">
                  <div className="value">Binary</div>
                  <div className="label">{t('proof.cards.tradingResults.stats.options')}</div>
                </div>
                <div className="stat">
                  <div className="value">Live</div>
                  <div className="label">{t('proof.cards.tradingResults.stats.account')}</div>
                </div>
              </div>
            </div>
          </ProofCard>

          <ProofCard>
            <div className="proof-image">
              <img src={Image3} alt="Strategy Analysis" style={{width: '90%', height: '90%', objectFit: 'cover'}} />
            </div>
            <div className="proof-content">
              <h3>{t('proof.cards.strategyExecution.title')}</h3>
              <p>{t('proof.cards.strategyExecution.description')}</p>
              <div className="stats">
                <div className="stat">
                  <div className="value">Price</div>
                  <div className="label">{t('proof.cards.strategyExecution.stats.levels')}</div>
                </div>
                <div className="stat">
                  <div className="value">Momentum</div>
                  <div className="label">{t('proof.cards.strategyExecution.stats.confirm')}</div>
                </div>
                <div className="stat">
                  <div className="value">Precise</div>
                  <div className="label">{t('proof.cards.strategyExecution.stats.entry')}</div>
                </div>
              </div>
            </div>
          </ProofCard>



        </ProofGrid>

        <CTASection>
          <h2>{t('proof.ctaTitle')}</h2>
          <p>
            {t('proof.ctaDescription')}
          </p>
          <CTAButton
            href={primaryAffiliateUrl}
            target="_blank"
            rel="noopener noreferrer"
            onClick={() => trackButtonClick('cta_proof', '/proof')}
          >
            {t('proof.cta')}
          </CTAButton>
        </CTASection>
        </ProofContent>
      </div>
    </ProofContainer>
  );
};

export default ProofPage;
