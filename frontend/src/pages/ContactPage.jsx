import { useEffect } from 'react';
import styled from 'styled-components';
import { useAnalytics } from '../hooks/useAnalytics';

const ContactContainer = styled.div`
  min-height: 100vh;
  padding: var(--spacing-12) 0;
  background: var(--gray-100);
`;

const ContactContent = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
`;

const HeroSection = styled.section`
  text-align: center;
  margin-bottom: var(--spacing-16);
  
  h1 {
    font-size: var(--font-size-4xl);
    margin-bottom: var(--spacing-6);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .subtitle {
    font-size: var(--font-size-xl);
    color: var(--gray-600);
    margin-bottom: var(--spacing-8);
  }
`;

const ContactGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-12);
  margin-bottom: var(--spacing-16);
  max-width: 800px;
  margin: 0 auto var(--spacing-16) auto;
`;

const InfoSection = styled.section`
  background: var(--white);
  border-radius: var(--radius-xl);
  padding: var(--spacing-8);
  box-shadow: var(--shadow-lg);
  
  h2 {
    margin-bottom: var(--spacing-6);
    color: var(--gray-900);
  }
`;

const InfoGrid = styled.div`
  display: grid;
  gap: var(--spacing-6);
`;

const InfoCard = styled.div`
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-4);
  
  .icon {
    font-size: 1.5rem;
    color: var(--primary-blue);
    margin-top: var(--spacing-1);
  }
  
  .content {
    flex: 1;
    
    h4 {
      margin-bottom: var(--spacing-2);
      color: var(--gray-900);
    }
    
    p {
      color: var(--gray-600);
      line-height: 1.5;
    }
    
    a {
      color: var(--primary-blue);
      text-decoration: none;
      
      &:hover {
        text-decoration: underline;
      }
    }
  }
`;

const FAQSection = styled.section`
  background: var(--white);
  border-radius: var(--radius-xl);
  padding: var(--spacing-8);
  box-shadow: var(--shadow-lg);
  
  h2 {
    text-align: center;
    margin-bottom: var(--spacing-8);
    color: var(--gray-900);
  }
`;

const FAQGrid = styled.div`
  display: grid;
  gap: var(--spacing-6);
`;

const FAQItem = styled.div`
  border-bottom: 1px solid var(--gray-200);
  padding-bottom: var(--spacing-4);
  
  &:last-child {
    border-bottom: none;
    padding-bottom: 0;
  }
  
  h4 {
    margin-bottom: var(--spacing-3);
    color: var(--primary-blue);
  }
  
  p {
    color: var(--gray-600);
    line-height: 1.6;
  }
`;

const ContactPage = () => {
  const { trackPageView } = useAnalytics();

  useEffect(() => {
    trackPageView('/contact');
  }, [trackPageView]);

  return (
    <ContactContainer>
      <ContactContent>
        <HeroSection>
          <h1>Contact SecondStrike</h1>
          <p className="subtitle">
            Have questions about our copy trading service? Contact us directly via email or Telegram for personalized support.
          </p>
        </HeroSection>

        <ContactGrid>
          <InfoSection>
            <h2>Contact Information</h2>
            <InfoGrid>
              <InfoCard>
                <div className="icon">📧</div>
                <div className="content">
                  <h4>Email Support</h4>
                  <p>
                    For general inquiries and support:<br />
                    <a href="mailto:<EMAIL>"><EMAIL></a>
                  </p>
                </div>
              </InfoCard>
              
              <InfoCard>
                <div className="icon">📱</div>
                <div className="content">
                  <h4>Telegram</h4>
                  <p>
                    Contact us directly for copy trading support:<br />
                    <a href="https://t.me/SecondStrikeCopy" target="_blank" rel="noopener noreferrer">
                      @SecondStrikeCopy
                    </a>
                  </p>
                </div>
              </InfoCard>
              
              <InfoCard>
                <div className="icon">🕒</div>
                <div className="content">
                  <h4>Response Time</h4>
                  <p>
                    We typically respond to all inquiries within 24 hours during business days.
                  </p>
                </div>
              </InfoCard>
            </InfoGrid>
          </InfoSection>
        </ContactGrid>

        <FAQSection>
          <h2>Frequently Asked Questions</h2>
          <FAQGrid>
            <FAQItem>
              <h4>How does the copy trading service work?</h4>
              <p>
                Our pro trader executes trades with small position sizes that can be copied proportionally
                by accounts of any size. You can start with just $100 or scale infinitely.
              </p>
            </FAQItem>
            
            <FAQItem>
              <h4>What is the minimum account size required?</h4>
              <p>
                You can start copying our strategy with as little as $100. Our analyst trades with small 
                positions specifically to accommodate traders with smaller accounts.
              </p>
            </FAQItem>
            
            <FAQItem>
              <h4>Can I scale up the position sizes?</h4>
              <p>
                Yes! Our system is designed to be scalable. You can copy trades at any
                multiplier that fits your account size and risk tolerance - there's no limit.
              </p>
            </FAQItem>
            
            <FAQItem>
              <h4>What money management system do you use?</h4>
              <p>
                We use a proprietary combination of Kelly Criterion for optimal position sizing and 
                modified Martingale for recovery sequences, creating one of the best money management 
                systems available.
              </p>
            </FAQItem>
            
            <FAQItem>
              <h4>Is this suitable for beginners?</h4>
              <p>
                Absolutely! Our copy trading service is perfect for beginners who want to learn while 
                following a proven strategy. We also provide educational content to help you understand 
                the methodology.
              </p>
            </FAQItem>
            
            <FAQItem>
              <h4>What are the risks involved?</h4>
              <p>
                All trading involves risk, and you could lose your entire investment. Past performance 
                does not guarantee future results. Only trade with money you can afford to lose.
              </p>
            </FAQItem>
          </FAQGrid>
        </FAQSection>
      </ContactContent>
    </ContactContainer>
  );
};

export default ContactPage;
