import { useEffect } from 'react';
import styled from 'styled-components';
import { useAnalytics } from '../hooks/useAnalytics';
import { useLanguage } from '../hooks/useLanguage';

const ContactContainer = styled.div`
  min-height: 100vh;
  padding: var(--spacing-12) 0;
  background: var(--gray-100);
`;

const ContactContent = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
`;

const HeroSection = styled.section`
  text-align: center;
  margin-bottom: var(--spacing-16);
  
  h1 {
    font-size: var(--font-size-4xl);
    margin-bottom: var(--spacing-6);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .subtitle {
    font-size: var(--font-size-xl);
    color: var(--gray-600);
    margin-bottom: var(--spacing-8);
  }
`;

const ContactGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-12);
  margin-bottom: var(--spacing-16);
  max-width: 800px;
  margin: 0 auto var(--spacing-16) auto;
`;

const InfoSection = styled.section`
  background: var(--white);
  border-radius: var(--radius-xl);
  padding: var(--spacing-8);
  box-shadow: var(--shadow-lg);
  
  h2 {
    margin-bottom: var(--spacing-6);
    color: var(--gray-900);
  }
`;

const InfoGrid = styled.div`
  display: grid;
  gap: var(--spacing-6);
`;

const InfoCard = styled.div`
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-4);
  
  .icon {
    font-size: 1.5rem;
    color: var(--primary-blue);
    margin-top: var(--spacing-1);
  }
  
  .content {
    flex: 1;
    
    h4 {
      margin-bottom: var(--spacing-2);
      color: var(--gray-900);
    }
    
    p {
      color: var(--gray-600);
      line-height: 1.5;
    }
    
    a {
      color: var(--primary-blue);
      text-decoration: none;
      
      &:hover {
        text-decoration: underline;
      }
    }
  }
`;

const FAQSection = styled.section`
  background: var(--white);
  border-radius: var(--radius-xl);
  padding: var(--spacing-8);
  box-shadow: var(--shadow-lg);
  
  h2 {
    text-align: center;
    margin-bottom: var(--spacing-8);
    color: var(--gray-900);
  }
`;

const FAQGrid = styled.div`
  display: grid;
  gap: var(--spacing-6);
`;

const FAQItem = styled.div`
  border-bottom: 1px solid var(--gray-200);
  padding-bottom: var(--spacing-4);
  
  &:last-child {
    border-bottom: none;
    padding-bottom: 0;
  }
  
  h4 {
    margin-bottom: var(--spacing-3);
    color: var(--primary-blue);
  }
  
  p {
    color: var(--gray-600);
    line-height: 1.6;
  }
`;

const ContactPage = () => {
  const { trackPageView } = useAnalytics();
  const { t } = useLanguage();

  useEffect(() => {
    trackPageView('/contact');
  }, [trackPageView]);

  return (
    <ContactContainer>
      <ContactContent>
        <HeroSection>
          <h1>{t('contact.title')}</h1>
          <p className="subtitle">
            {t('contact.subtitle')}
          </p>
        </HeroSection>

        <ContactGrid>
          <InfoSection>
            <h2>{t('contact.info.title')}</h2>
            <InfoGrid>
              <InfoCard>
                <div className="icon">📧</div>
                <div className="content">
                  <h4>{t('contact.info.email.title')}</h4>
                  <p>
                    {t('contact.info.email.description')}<br />
                    <a href="mailto:<EMAIL>">{t('contact.info.email.address')}</a>
                  </p>
                </div>
              </InfoCard>

              <InfoCard>
                <div className="icon">📱</div>
                <div className="content">
                  <h4>{t('contact.info.telegram.title')}</h4>
                  <p>
                    {t('contact.info.telegram.description')}<br />
                    <a href="https://t.me/SecondStrikeCopy" target="_blank" rel="noopener noreferrer">
                      {t('contact.info.telegram.handle')}
                    </a>
                  </p>
                </div>
              </InfoCard>

              <InfoCard>
                <div className="icon">🕒</div>
                <div className="content">
                  <h4>{t('contact.info.responseTime.title')}</h4>
                  <p>
                    {t('contact.info.responseTime.description')}
                  </p>
                </div>
              </InfoCard>
            </InfoGrid>
          </InfoSection>
        </ContactGrid>

        <FAQSection>
          <h2>{t('contact.faq.title')}</h2>
          <FAQGrid>
            {t('contact.faq.items').map((item, index) => (
              <FAQItem key={index}>
                <h4>{item.question}</h4>
                <p>{item.answer}</p>
              </FAQItem>
            ))}
          </FAQGrid>
        </FAQSection>
      </ContactContent>
    </ContactContainer>
  );
};

export default ContactPage;
