// Removed API dependency - now using only client-side analytics

class AnalyticsService {
  constructor() {
    this.sessionId = this.generateSessionId();
    this.utmParams = this.getUTMParams();
    this.initialized = false;
  }

  // Initialize analytics
  initialize() {
    if (this.initialized) return;
    
    // Initialize Google Analytics if consent is given
    this.initializeGoogleAnalytics();
    
    // Set up scroll depth tracking
    this.setupScrollTracking();
    
    this.initialized = true;
  }

  // Initialize Google Analytics
  initializeGoogleAnalytics() {
    const gaId = import.meta.env.VITE_GA_MEASUREMENT_ID;
    if (!gaId) return;

    // Load Google Analytics script
    const script = document.createElement('script');
    script.async = true;
    script.src = `https://www.googletagmanager.com/gtag/js?id=${gaId}`;
    document.head.appendChild(script);

    // Initialize gtag
    window.dataLayer = window.dataLayer || [];
    function gtag() {
      window.dataLayer.push(arguments);
    }
    window.gtag = gtag;

    gtag('js', new Date());
    gtag('config', gaId, {
      send_page_view: false, // We'll handle page views manually
      cookie_flags: 'SameSite=None;Secure'
    });

    // Set default consent state
    gtag('consent', 'default', {
      analytics_storage: 'denied',
      ad_storage: 'denied'
    });
  }

  // Generate unique session ID
  generateSessionId() {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Get UTM parameters from URL
  getUTMParams() {
    const urlParams = new URLSearchParams(window.location.search);
    return {
      utm_source: urlParams.get('utm_source'),
      utm_medium: urlParams.get('utm_medium'),
      utm_campaign: urlParams.get('utm_campaign'),
      utm_term: urlParams.get('utm_term'),
      utm_content: urlParams.get('utm_content')
    };
  }

  // Track page view
  async trackPageView(page, additionalData = {}) {
    try {
      // Track in our backend
      await this.trackEvent('page_view', page, additionalData);
      
      // Track in Google Analytics if available
      if (window.gtag) {
        window.gtag('event', 'page_view', {
          page_title: document.title,
          page_location: window.location.href,
          page_path: page
        });
      }
    } catch (error) {
      console.error('Error tracking page view:', error);
    }
  }

  // Track custom event
  async trackEvent(event, page, metadata = {}) {
    try {
      const eventData = {
        event,
        page: page || window.location.pathname,
        sessionId: this.sessionId,
        utmParams: this.utmParams,
        userAgent: navigator.userAgent,
        referrer: document.referrer,
        metadata: {
          timestamp: new Date().toISOString(),
          viewport: {
            width: window.innerWidth,
            height: window.innerHeight
          },
          ...metadata
        }
      };

      // Send to backend
      await api.post('/analytics/track', eventData);
      
      // Track in Google Analytics if available
      if (window.gtag) {
        window.gtag('event', event, {
          event_category: 'engagement',
          event_label: page,
          custom_parameters: metadata
        });
      }
    } catch (error) {
      console.error('Error tracking event:', error);
    }
  }

  // Set up scroll depth tracking
  setupScrollTracking() {
    let maxScroll = 0;
    const thresholds = [25, 50, 75, 90, 100];
    const tracked = new Set();

    const trackScroll = () => {
      const scrollPercent = Math.round(
        (window.scrollY / (document.documentElement.scrollHeight - window.innerHeight)) * 100
      );
      
      if (scrollPercent > maxScroll) {
        maxScroll = scrollPercent;
        
        // Track threshold crossings
        thresholds.forEach(threshold => {
          if (scrollPercent >= threshold && !tracked.has(threshold)) {
            tracked.add(threshold);
            this.trackEvent('scroll_depth', window.location.pathname, {
              depth: threshold
            });
          }
        });
      }
    };

    // Throttle scroll events
    let ticking = false;
    const handleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          trackScroll();
          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
  }

  // Track button clicks
  trackButtonClick(buttonName, page, additionalData = {}) {
    return this.trackEvent('button_click', page, {
      buttonName,
      ...additionalData
    });
  }

  // Track form submissions
  trackFormSubmit(formName, page, additionalData = {}) {
    return this.trackEvent('form_submit', page, {
      formName,
      ...additionalData
    });
  }

  // Track video interactions
  trackVideoPlay(videoId, page, additionalData = {}) {
    return this.trackEvent('video_play', page, {
      videoId,
      ...additionalData
    });
  }

  trackVideoComplete(videoId, page, additionalData = {}) {
    return this.trackEvent('video_complete', page, {
      videoId,
      ...additionalData
    });
  }

  // Track affiliate link clicks
  trackAffiliateClick(linkId, page, additionalData = {}) {
    return this.trackEvent('affiliate_click', page, {
      linkId,
      ...additionalData
    });
  }

  // Track lead capture
  trackLeadCapture(source, page, additionalData = {}) {
    return this.trackEvent('lead_capture', page, {
      source,
      ...additionalData
    });
  }

  // Get analytics dashboard data (now returns mock data since no backend)
  async getDashboardData(dateRange = {}) {
    // Return mock data for dashboard - in a real funnel, this would come from Google Analytics API
    return {
      visitors: 1250,
      conversions: 89,
      conversionRate: 7.12,
      revenue: 4450,
      topPages: [
        { page: '/', views: 650, conversions: 45 },
        { page: '/proof', views: 420, conversions: 28 },
        { page: '/pitch', views: 180, conversions: 16 }
      ]
    };
  }

  // Get conversion metrics
  async getConversionMetrics(dateRange = {}) {
    try {
      const response = await api.get('/analytics/conversions', {
        params: dateRange
      });
      return response;
    } catch (error) {
      console.error('Error fetching conversion metrics:', error);
      throw error;
    }
  }
}

export const analyticsService = new AnalyticsService();
