import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';
import { AnalyticsProvider } from './contexts/AnalyticsContext';
import { ComplianceProvider } from './contexts/ComplianceContext';
import { LanguageProvider } from './contexts/LanguageContext';

// Layout Components
import Header from './components/layout/Header';
import Footer from './components/layout/Footer';
import CookieConsent from './components/compliance/CookieConsent';
import ChatWidget from './components/lead/ChatWidget';
import ScrollToTop from './components/common/ScrollToTop';

// Pages
import LandingPage from './pages/LandingPage';
import ProofPage from './pages/ProofPage';
import PitchPage from './pages/PitchPage';

import PrivacyPolicy from './pages/PrivacyPolicy';
import TermsOfService from './pages/TermsOfService';
import ContactPage from './pages/ContactPage';
import SetupGuidePage from './pages/SetupGuidePage';
import NotFound from './pages/NotFound';

// Removed protected routes - this is now a static funnel

// Global Styles
import './styles/global.css';

function App() {
  return (
    <LanguageProvider>
      <AnalyticsProvider>
        <ComplianceProvider>
            <Router>
            <ScrollToTop />
            <div className="app">
              <Header />

              <main className="main-content">
                <Routes>
                  <Route path="/" element={<LandingPage />} />
                  <Route path="/proof" element={<ProofPage />} />
                  <Route path="/pitch" element={<PitchPage />} />
                  <Route path="/privacy-policy" element={<PrivacyPolicy />} />
                  <Route path="/terms-of-service" element={<TermsOfService />} />
                  <Route path="/contact" element={<ContactPage />} />
                  <Route path="/setup-guide" element={<SetupGuidePage />} />

                  {/* Removed dashboard - not needed for static funnel */}

                  <Route path="*" element={<NotFound />} />
                </Routes>
              </main>

              <Footer />
              <ChatWidget />
              <CookieConsent />
              <Toaster
                position="top-right"
                toastOptions={{
                  duration: 4000,
                  style: {
                    background: '#363636',
                    color: '#fff',
                  },
                }}
              />
            </div>
          </Router>
        </ComplianceProvider>
      </AnalyticsProvider>
    </LanguageProvider>
  );
}

export default App;
