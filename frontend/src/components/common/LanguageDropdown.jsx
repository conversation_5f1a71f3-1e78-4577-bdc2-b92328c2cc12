import { useState, useRef, useEffect } from 'react';
import styled from 'styled-components';
import { useLanguage } from '../../hooks/useLanguage';

const DropdownContainer = styled.div`
  position: relative;
  display: inline-block;
`;

const DropdownButton = styled.button`
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  background: transparent;
  border: 1px solid var(--white);
  color: var(--white);
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--radius-md);
  cursor: pointer;
  font-size: var(--font-size-sm);
  transition: all var(--transition-fast);
  
  &:hover {
    background: var(--white);
    color: var(--primary-blue);
  }
  
  .arrow {
    transition: transform var(--transition-fast);
    transform: ${props => props.isOpen ? 'rotate(180deg)' : 'rotate(0deg)'};
  }
`;

const DropdownMenu = styled.div`
  position: absolute;
  top: 100%;
  right: 0;
  background: var(--white);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);
  min-width: 160px;
  z-index: 1000;
  opacity: ${props => props.isOpen ? 1 : 0};
  visibility: ${props => props.isOpen ? 'visible' : 'hidden'};
  transform: ${props => props.isOpen ? 'translateY(0)' : 'translateY(-10px)'};
  transition: all var(--transition-fast);
  margin-top: var(--spacing-1);
`;

const DropdownItem = styled.button`
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  width: 100%;
  padding: var(--spacing-3) var(--spacing-4);
  background: none;
  border: none;
  text-align: left;
  cursor: pointer;
  font-size: var(--font-size-sm);
  color: var(--gray-700);
  transition: background-color var(--transition-fast);
  
  &:hover {
    background: var(--gray-100);
  }
  
  &:first-child {
    border-radius: var(--radius-md) var(--radius-md) 0 0;
  }
  
  &:last-child {
    border-radius: 0 0 var(--radius-md) var(--radius-md);
  }
  
  &.active {
    background: var(--primary-blue);
    color: var(--white);
    
    &:hover {
      background: var(--primary-blue);
    }
  }
  
  .flag {
    font-size: var(--font-size-base);
  }
`;

const LanguageDropdown = () => {
  const [isOpen, setIsOpen] = useState(false);
  const { currentLanguage, availableLanguages, changeLanguage } = useLanguage();
  const dropdownRef = useRef(null);

  const currentLang = availableLanguages.find(lang => lang.code === currentLanguage);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleLanguageChange = (languageCode) => {
    changeLanguage(languageCode);
    setIsOpen(false);
  };

  return (
    <DropdownContainer ref={dropdownRef}>
      <DropdownButton 
        onClick={() => setIsOpen(!isOpen)}
        isOpen={isOpen}
      >
        <span className="flag">{currentLang?.flag}</span>
        <span>{currentLang?.name}</span>
        <span className="arrow">▼</span>
      </DropdownButton>
      
      <DropdownMenu isOpen={isOpen}>
        {availableLanguages.map((language) => (
          <DropdownItem
            key={language.code}
            onClick={() => handleLanguageChange(language.code)}
            className={currentLanguage === language.code ? 'active' : ''}
          >
            <span className="flag">{language.flag}</span>
            <span>{language.name}</span>
          </DropdownItem>
        ))}
      </DropdownMenu>
    </DropdownContainer>
  );
};

export default LanguageDropdown;
