import React, { useState } from 'react';
import styled from 'styled-components';
import { useAnalytics } from '../../hooks/useAnalytics';
import toast from 'react-hot-toast';

const FormContainer = styled.div`
  background: var(--white);
  border-radius: var(--radius-xl);
  padding: var(--spacing-8);
  box-shadow: var(--shadow-lg);
  max-width: 600px;
  margin: 0 auto;
`;

const FormTitle = styled.h2`
  text-align: center;
  margin-bottom: var(--spacing-6);
  color: var(--gray-900);
`;

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
`;

const Label = styled.label`
  font-weight: 600;
  margin-bottom: var(--spacing-2);
  color: var(--gray-700);
`;

const Input = styled.input`
  padding: var(--spacing-3);
  border: 2px solid var(--gray-300);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  transition: border-color var(--transition-fast);
  
  &:focus {
    outline: none;
    border-color: var(--primary-blue);
  }
  
  &.error {
    border-color: var(--error);
  }
`;

const TextArea = styled.textarea`
  padding: var(--spacing-3);
  border: 2px solid var(--gray-300);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  min-height: 120px;
  resize: vertical;
  font-family: inherit;
  transition: border-color var(--transition-fast);
  
  &:focus {
    outline: none;
    border-color: var(--primary-blue);
  }
  
  &.error {
    border-color: var(--error);
  }
`;

const Select = styled.select`
  padding: var(--spacing-3);
  border: 2px solid var(--gray-300);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  background: var(--white);
  transition: border-color var(--transition-fast);
  
  &:focus {
    outline: none;
    border-color: var(--primary-blue);
  }
`;

const SubmitButton = styled.button`
  background: var(--primary-blue);
  color: var(--white);
  padding: var(--spacing-4);
  border: none;
  border-radius: var(--radius-lg);
  font-size: var(--font-size-lg);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
  
  &:hover {
    background: var(--primary-blue-dark);
    transform: translateY(-2px);
  }
  
  &:disabled {
    background: var(--gray-400);
    cursor: not-allowed;
    transform: none;
  }
`;

const ErrorMessage = styled.span`
  color: var(--error);
  font-size: var(--font-size-sm);
  margin-top: var(--spacing-1);
`;

const ContactForm = () => {
  const { trackButtonClick } = useAnalytics();
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: 'general',
    message: ''
  });
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    }
    
    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }
    
    if (!formData.message.trim()) {
      newErrors.message = 'Message is required';
    } else if (formData.message.trim().length < 10) {
      newErrors.message = 'Message must be at least 10 characters long';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setIsSubmitting(true);
    trackButtonClick('contact_form_submit', '/contact');
    
    try {
      // Create mailto link with form data
      const subject = encodeURIComponent(`SecondStrike Contact: ${formData.subject}`);
      const body = encodeURIComponent(`Name: ${formData.name}\nEmail: ${formData.email}\n\nMessage:\n${formData.message}`);
      const mailtoLink = `mailto:<EMAIL>?subject=${subject}&body=${body}`;

      // Open default email client
      window.location.href = mailtoLink;

      toast.success('Opening your email client to send the message!');
      setFormData({
        name: '',
        email: '',
        subject: 'general',
        message: ''
      });
    } catch (error) {
      toast.error('Failed to open email client. <NAME_EMAIL> directly.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <FormContainer>
      <FormTitle>Contact Our Team</FormTitle>
      <Form onSubmit={handleSubmit}>
        <FormGroup>
          <Label htmlFor="name">Full Name *</Label>
          <Input
            type="text"
            id="name"
            name="name"
            value={formData.name}
            onChange={handleChange}
            className={errors.name ? 'error' : ''}
            placeholder="Enter your full name"
          />
          {errors.name && <ErrorMessage>{errors.name}</ErrorMessage>}
        </FormGroup>

        <FormGroup>
          <Label htmlFor="email">Email Address *</Label>
          <Input
            type="email"
            id="email"
            name="email"
            value={formData.email}
            onChange={handleChange}
            className={errors.email ? 'error' : ''}
            placeholder="Enter your email address"
          />
          {errors.email && <ErrorMessage>{errors.email}</ErrorMessage>}
        </FormGroup>

        <FormGroup>
          <Label htmlFor="subject">Subject</Label>
          <Select
            id="subject"
            name="subject"
            value={formData.subject}
            onChange={handleChange}
          >
            <option value="general">General Inquiry</option>
            <option value="strategy">Strategy Questions</option>
            <option value="technical">Technical Support</option>
            <option value="account">Account Issues</option>
            <option value="partnership">Partnership Opportunities</option>
          </Select>
        </FormGroup>

        <FormGroup>
          <Label htmlFor="message">Message *</Label>
          <TextArea
            id="message"
            name="message"
            value={formData.message}
            onChange={handleChange}
            className={errors.message ? 'error' : ''}
            placeholder="Tell us how we can help you..."
          />
          {errors.message && <ErrorMessage>{errors.message}</ErrorMessage>}
        </FormGroup>

        <SubmitButton type="submit" disabled={isSubmitting}>
          {isSubmitting ? 'Sending...' : 'Send Message'}
        </SubmitButton>
      </Form>
    </FormContainer>
  );
};

export default ContactForm;
