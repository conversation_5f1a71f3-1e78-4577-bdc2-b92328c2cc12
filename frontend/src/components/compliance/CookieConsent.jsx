import React, { useState } from 'react';
import styled from 'styled-components';
import { useCompliance } from '../../hooks/useCompliance';

const ConsentBanner = styled.div`
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: var(--gray-900);
  color: var(--white);
  padding: var(--spacing-4);
  box-shadow: var(--shadow-xl);
  z-index: 1000;
  transform: translateY(${props => props.show ? '0' : '100%'});
  transition: transform var(--transition-normal);
`;

const ConsentContent = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-4);
  
  @media (max-width: 768px) {
    flex-direction: column;
    text-align: center;
  }
`;

const ConsentText = styled.div`
  flex: 1;
  
  p {
    margin: 0 0 var(--spacing-2) 0;
    font-size: var(--font-size-sm);
  }
  
  a {
    color: var(--primary-blue);
    text-decoration: underline;
  }
`;

const ConsentActions = styled.div`
  display: flex;
  gap: var(--spacing-3);
  align-items: center;
  
  @media (max-width: 768px) {
    flex-wrap: wrap;
    justify-content: center;
  }
`;

const ConsentButton = styled.button`
  padding: var(--spacing-2) var(--spacing-4);
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
  white-space: nowrap;
  
  &.primary {
    background: var(--primary-blue);
    color: var(--white);
    
    &:hover {
      background: var(--primary-purple);
    }
  }
  
  &.secondary {
    background: var(--gray-700);
    color: var(--white);
    
    &:hover {
      background: var(--gray-600);
    }
  }
  
  &.outline {
    background: transparent;
    color: var(--white);
    border: 1px solid var(--gray-600);
    
    &:hover {
      background: var(--gray-800);
    }
  }
`;

const PreferencesModal = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1001;
  padding: var(--spacing-4);
`;

const PreferencesContent = styled.div`
  background: var(--white);
  border-radius: var(--radius-xl);
  padding: var(--spacing-6);
  max-width: 500px;
  width: 100%;
  max-height: 80vh;
  overflow-y: auto;
`;

const PreferenceItem = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-3) 0;
  border-bottom: 1px solid var(--gray-200);
  
  &:last-child {
    border-bottom: none;
  }
`;

const PreferenceLabel = styled.div`
  flex: 1;
  
  h4 {
    margin: 0 0 var(--spacing-1) 0;
    font-size: var(--font-size-base);
  }
  
  p {
    margin: 0;
    font-size: var(--font-size-sm);
    color: var(--gray-600);
  }
`;

const Toggle = styled.label`
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
  
  input {
    opacity: 0;
    width: 0;
    height: 0;
  }
  
  span {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--gray-300);
    transition: var(--transition-fast);
    border-radius: 24px;
    
    &:before {
      position: absolute;
      content: "";
      height: 18px;
      width: 18px;
      left: 3px;
      bottom: 3px;
      background-color: var(--white);
      transition: var(--transition-fast);
      border-radius: 50%;
    }
  }
  
  input:checked + span {
    background-color: var(--primary-blue);
  }
  
  input:checked + span:before {
    transform: translateX(26px);
  }
`;

const CookieConsent = () => {
  const { shouldShowCookieConsent, saveCookieConsent } = useCompliance();
  const [showPreferences, setShowPreferences] = useState(false);
  const [preferences, setPreferences] = useState({
    necessary: true, // Always required
    analytics: false,
    marketing: false
  });

  if (!shouldShowCookieConsent()) {
    return null;
  }

  const handleAcceptAll = async () => {
    const allPreferences = {
      necessary: true,
      analytics: true,
      marketing: true
    };
    
    try {
      await saveCookieConsent(allPreferences);
    } catch (error) {
      console.error('Error saving cookie consent:', error);
    }
  };

  const handleRejectAll = async () => {
    const minimalPreferences = {
      necessary: true,
      analytics: false,
      marketing: false
    };
    
    try {
      await saveCookieConsent(minimalPreferences);
    } catch (error) {
      console.error('Error saving cookie consent:', error);
    }
  };

  const handleSavePreferences = async () => {
    try {
      await saveCookieConsent(preferences);
      setShowPreferences(false);
    } catch (error) {
      console.error('Error saving cookie preferences:', error);
    }
  };

  const handlePreferenceChange = (key, value) => {
    setPreferences(prev => ({
      ...prev,
      [key]: value
    }));
  };

  return (
    <>
      <ConsentBanner show={true}>
        <ConsentContent>
          <ConsentText>
            <p>
              <strong>🍪 We use cookies to enhance your experience</strong>
            </p>
            <p>
              We use cookies for analytics and to improve your trading experience. 
              By clicking "Accept All", you consent to our use of cookies. 
              <a href="/privacy-policy" target="_blank">Learn more</a>
            </p>
          </ConsentText>
          
          <ConsentActions>
            <ConsentButton 
              className="outline" 
              onClick={() => setShowPreferences(true)}
            >
              Preferences
            </ConsentButton>
            <ConsentButton 
              className="secondary" 
              onClick={handleRejectAll}
            >
              Reject All
            </ConsentButton>
            <ConsentButton 
              className="primary" 
              onClick={handleAcceptAll}
            >
              Accept All
            </ConsentButton>
          </ConsentActions>
        </ConsentContent>
      </ConsentBanner>

      {showPreferences && (
        <PreferencesModal onClick={(e) => e.target === e.currentTarget && setShowPreferences(false)}>
          <PreferencesContent>
            <h3>Cookie Preferences</h3>
            <p>Choose which cookies you want to accept. You can change these settings at any time.</p>
            
            <PreferenceItem>
              <PreferenceLabel>
                <h4>Necessary Cookies</h4>
                <p>Required for the website to function properly. Cannot be disabled.</p>
              </PreferenceLabel>
              <Toggle>
                <input type="checkbox" checked={true} disabled />
                <span></span>
              </Toggle>
            </PreferenceItem>
            
            <PreferenceItem>
              <PreferenceLabel>
                <h4>Analytics Cookies</h4>
                <p>Help us understand how visitors interact with our website.</p>
              </PreferenceLabel>
              <Toggle>
                <input 
                  type="checkbox" 
                  checked={preferences.analytics}
                  onChange={(e) => handlePreferenceChange('analytics', e.target.checked)}
                />
                <span></span>
              </Toggle>
            </PreferenceItem>
            
            <PreferenceItem>
              <PreferenceLabel>
                <h4>Marketing Cookies</h4>
                <p>Used to track visitors and display relevant ads and content.</p>
              </PreferenceLabel>
              <Toggle>
                <input 
                  type="checkbox" 
                  checked={preferences.marketing}
                  onChange={(e) => handlePreferenceChange('marketing', e.target.checked)}
                />
                <span></span>
              </Toggle>
            </PreferenceItem>
            
            <div style={{ marginTop: 'var(--spacing-6)', display: 'flex', gap: 'var(--spacing-3)', justifyContent: 'flex-end' }}>
              <ConsentButton 
                className="secondary" 
                onClick={() => setShowPreferences(false)}
              >
                Cancel
              </ConsentButton>
              <ConsentButton 
                className="primary" 
                onClick={handleSavePreferences}
              >
                Save Preferences
              </ConsentButton>
            </div>
          </PreferencesContent>
        </PreferencesModal>
      )}
    </>
  );
};

export default CookieConsent;
