import { useState } from 'react';
import styled from 'styled-components';
import { useLanguage } from '../../hooks/useLanguage';

const ChatContainer = styled.div`
  position: fixed;
  bottom: var(--spacing-4);
  right: var(--spacing-4);
  z-index: 999;
`;

const ChatButton = styled.button`
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: var(--gradient-primary);
  color: var(--white);
  border: none;
  font-size: var(--font-size-xl);
  cursor: pointer;
  box-shadow: var(--shadow-lg);
  transition: all var(--transition-fast);
  
  &:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-xl);
  }
`;

const ChatWindow = styled.div`
  position: absolute;
  bottom: 70px;
  right: 0;
  width: 300px;
  height: 400px;
  background: var(--white);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  display: ${props => props.isOpen ? 'flex' : 'none'};
  flex-direction: column;
  overflow: hidden;
`;

const ChatHeader = styled.div`
  background: var(--gradient-primary);
  color: var(--white);
  padding: var(--spacing-4);
  text-align: center;
  
  h4 {
    margin: 0;
    font-size: var(--font-size-base);
  }
  
  p {
    margin: var(--spacing-1) 0 0 0;
    font-size: var(--font-size-sm);
    opacity: 0.9;
  }
`;

const ChatBody = styled.div`
  flex: 1;
  padding: var(--spacing-4);
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: center;
`;

const ReadyButton = styled.button`
  background: var(--gradient-primary);
  color: var(--white);
  border: none;
  padding: var(--spacing-3) var(--spacing-6);
  border-radius: var(--radius-lg);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
  margin: var(--spacing-2) 0;
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
  }
`;

const ChatWidget = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [hasResponded, setHasResponded] = useState(false);
  const { t } = useLanguage();

  const handleToggle = () => {
    setIsOpen(!isOpen);
  };

  const handleReady = () => {
    setHasResponded(true);
    // Redirect to affiliate link after showing success message
    setTimeout(() => {
      window.open(import.meta.env.VITE_PRIMARY_AFFILIATE_URL, '_blank');
      setIsOpen(false);
      setHasResponded(false);
    }, 2000);
  };

  return (
    <ChatContainer>
      <ChatWindow isOpen={isOpen}>
        <ChatHeader>
          <h4>{t('chat.title')}</h4>
          <p>{t('chat.subtitle')}</p>
        </ChatHeader>

        <ChatBody>
          {!hasResponded ? (
            <>
              <p>{t('chat.question')}</p>
              <ReadyButton onClick={handleReady}>
                {t('chat.ready')}
              </ReadyButton>
              <p style={{ fontSize: 'var(--font-size-xs)', color: 'var(--gray-500)' }}>
                {t('chat.bonus')}
              </p>
            </>
          ) : (
            <>
              <p>{t('chat.success')}</p>
              <p>{t('chat.redirecting')}</p>
              <p style={{ fontSize: 'var(--font-size-sm)', color: 'var(--success)' }}>
                {t('chat.opening')}
              </p>
            </>
          )}
        </ChatBody>
      </ChatWindow>
      
      <ChatButton onClick={handleToggle}>
        {isOpen ? '✕' : '💬'}
      </ChatButton>
    </ChatContainer>
  );
};

export default ChatWidget;
