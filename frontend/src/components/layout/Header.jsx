import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import styled from 'styled-components';
import { useAnalytics } from '../../hooks/useAnalytics';
import { useLanguage } from '../../hooks/useLanguage';
import LanguageDropdown from '../common/LanguageDropdown';

const HeaderContainer = styled.header`
  background: var(--gradient-primary);
  color: var(--white);
  padding: var(--spacing-4) 0;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: var(--shadow-md);
`;

const HeaderContent = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
`;

const Logo = styled(Link)`
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--white);
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  
  &:hover {
    color: var(--white);
    text-decoration: none;
  }
`;

const Nav = styled.nav`
  display: flex;
  align-items: center;
  gap: var(--spacing-6);
  
  @media (max-width: 768px) {
    display: ${props => props.isOpen ? 'flex' : 'none'};
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--gradient-primary);
    flex-direction: column;
    padding: var(--spacing-4);
    box-shadow: var(--shadow-lg);
  }
`;

const NavLink = styled(Link)`
  color: var(--white);
  text-decoration: none;
  font-weight: 500;
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--radius-md);
  transition: background-color var(--transition-fast);
  
  &:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--white);
    text-decoration: none;
  }
  
  &.active {
    background: rgba(255, 255, 255, 0.2);
  }
`;

const UserMenu = styled.div`
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
`;

const CTAButtons = styled.div`
  display: flex;
  align-items: center;
  gap: var(--spacing-2);

  @media (max-width: 768px) {
    display: none;
  }
`;

const CTAButton = styled.a`
  padding: var(--spacing-2) var(--spacing-4);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  font-weight: 600;
  text-decoration: none;
  transition: all var(--transition-fast);
  white-space: nowrap;

  &.primary {
    background: var(--white);
    color: var(--primary-blue);

    &:hover {
      background: var(--gray-100);
      transform: translateY(-1px);
    }
  }

  &.secondary {
    background: transparent;
    color: var(--white);
    border: 1px solid var(--white);

    &:hover {
      background: var(--white);
      color: var(--primary-blue);
    }
  }
`;

const MobileMenuButton = styled.button`
  display: none;
  background: none;
  border: none;
  color: var(--white);
  font-size: var(--font-size-xl);
  cursor: pointer;
  padding: var(--spacing-2);
  
  @media (max-width: 768px) {
    display: block;
  }
`;

const Header = () => {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const { trackButtonClick } = useAnalytics();
  const { t } = useLanguage();
  const location = useLocation();

  // No authentication needed for static funnel

  const handleMobileMenuToggle = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  const handleNavClick = (linkName) => {
    trackButtonClick(`nav_${linkName}`, location.pathname);
    setMobileMenuOpen(false);
  };

  const isActive = (path) => {
    return location.pathname === path ? 'active' : '';
  };

  return (
    <HeaderContainer>
      <HeaderContent>
        <Logo to="/" onClick={() => handleNavClick('logo')}>
          {t('header.logo')}
        </Logo>

        <Nav isOpen={mobileMenuOpen}>
          <NavLink
            to="/"
            className={isActive('/')}
            onClick={() => handleNavClick('home')}
          >
            {t('header.nav.home')}
          </NavLink>
          <NavLink
            to="/proof"
            className={isActive('/proof')}
            onClick={() => handleNavClick('proof')}
          >
            {t('header.nav.proof')}
          </NavLink>
          <NavLink
            to="/pitch"
            className={isActive('/pitch')}
            onClick={() => handleNavClick('pitch')}
          >
            {t('header.nav.strategy')}
          </NavLink>

          {/* Setup guide points to footer version */}
          <NavLink
            to="/setup-guide"
            className={isActive('/setup-guide')}
            onClick={() => handleNavClick('setup-guide')}
          >
            {t('header.nav.setupGuide')}
          </NavLink>
        </Nav>

        <UserMenu>
          <CTAButtons>
            <CTAButton
              href={import.meta.env.VITE_PRIMARY_AFFILIATE_URL}
              className="primary"
              onClick={() => trackButtonClick('header_cta', location.pathname)}
            >
              {t('header.cta')}
            </CTAButton>
          </CTAButtons>
          <LanguageDropdown />
          <MobileMenuButton onClick={handleMobileMenuToggle}>
            {mobileMenuOpen ? '✕' : '☰'}
          </MobileMenuButton>
        </UserMenu>
      </HeaderContent>
    </HeaderContainer>
  );
};

export default Header;
