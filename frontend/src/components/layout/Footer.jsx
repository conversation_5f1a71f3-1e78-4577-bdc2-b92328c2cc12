
import { Link } from 'react-router-dom';
import styled from 'styled-components';
import { useCompliance } from '../../hooks/useCompliance';
import { useLanguage } from '../../hooks/useLanguage';

const FooterContainer = styled.footer`
  background: var(--gray-900);
  color: var(--gray-300);
  padding: var(--spacing-12) 0 var(--spacing-6);
  margin-top: auto;
`;

const FooterContent = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
`;

const FooterGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-8);
  margin-bottom: var(--spacing-8);
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: var(--spacing-6);
  }
`;

const FooterSection = styled.div`
  h3 {
    color: var(--white);
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-4);
  }
  
  p, li {
    font-size: var(--font-size-sm);
    line-height: 1.6;
    margin-bottom: var(--spacing-2);
  }
  
  ul {
    list-style: none;
    padding: 0;
  }
  
  a {
    color: var(--gray-300);
    text-decoration: none;
    transition: color var(--transition-fast);
    
    &:hover {
      color: var(--primary-blue);
    }
  }
`;

const DisclaimerSection = styled.div`
  border-top: 1px solid var(--gray-700);
  padding-top: var(--spacing-6);
  margin-top: var(--spacing-8);
`;

const DisclaimerBox = styled.div`
  background: var(--gray-800);
  border: 1px solid var(--gray-700);
  border-radius: var(--radius-lg);
  padding: var(--spacing-4);
  margin-bottom: var(--spacing-4);
  
  h4 {
    color: var(--warning);
    font-size: var(--font-size-base);
    margin-bottom: var(--spacing-2);
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
  }
  
  p {
    font-size: var(--font-size-sm);
    margin-bottom: 0;
  }
`;

const Copyright = styled.div`
  text-align: center;
  padding-top: var(--spacing-4);
  border-top: 1px solid var(--gray-700);
  font-size: var(--font-size-sm);
  color: var(--gray-500);
`;

const Footer = () => {
  const { disclaimers } = useCompliance();
  const { t } = useLanguage();

  return (
    <FooterContainer>
      <FooterContent>
        <FooterGrid>
          <FooterSection>
            <h3>{t('header.logo')}</h3>
            <p>
              {t('footer.description')}
            </p>
            <p>
              <strong>{t('footer.performance')}</strong><br />
              {t('footer.performanceNote')}
            </p>
          </FooterSection>
          
          <FooterSection>
            <h3>{t('footer.quickLinks')}</h3>
            <ul>
              <li><Link to="/">{t('header.nav.home')}</Link></li>
              <li><Link to="/proof">{t('header.nav.proof')}</Link></li>
              <li><Link to="/pitch">{t('header.nav.strategy')}</Link></li>
              <li><Link to="/setup-guide">{t('header.nav.setupGuide')}</Link></li>
              <li><a href={import.meta.env.VITE_PRIMARY_AFFILIATE_URL} target="_blank" rel="noopener noreferrer">{t('footer.startTrading')}</a></li>
            </ul>
          </FooterSection>

          <FooterSection>
            <h3>{t('footer.support')}</h3>
            <ul>
              <li><Link to="/contact">{t('footer.contact')}</Link></li>
              <li><a href="https://t.me/SecondStrikeCopy" target="_blank" rel="noopener noreferrer">{t('footer.telegram')}</a></li>
              <li><Link to="/privacy-policy">{t('footer.privacyPolicy')}</Link></li>
              <li><Link to="/terms-of-service">{t('footer.termsOfService')}</Link></li>
            </ul>
          </FooterSection>
          
          <FooterSection>
            <h3>Trading Hours</h3>
            <p>
              <strong>Market Hours:</strong><br />
              Monday - Friday: 24/5<br />
              Weekend: Limited assets
            </p>
            <p>
              <strong>Support Hours:</strong><br />
              Monday - Friday: 9 AM - 6 PM CET
            </p>
          </FooterSection>
        </FooterGrid>
        
        <DisclaimerSection>
          <DisclaimerBox>
            <h4>{t('footer.disclaimers.risk')}</h4>
            <p>
              {disclaimers?.riskWarning || t('footer.disclaimers.riskText')}
            </p>
          </DisclaimerBox>

          <DisclaimerBox>
            <h4>{t('footer.disclaimers.country')}</h4>
            <p>
              {disclaimers?.countryRestriction || t('footer.disclaimers.countryText')}
            </p>
          </DisclaimerBox>

          <DisclaimerBox>
            <h4>{t('footer.disclaimers.affiliate')}</h4>
            <p>
              {disclaimers?.affiliateDisclosure || t('footer.disclaimers.affiliateText')}
            </p>
          </DisclaimerBox>
        </DisclaimerSection>
        
        <Copyright>
          <p>
            {t('footer.copyright')}
          </p>
          <p>
            <Link to="/contact">{t('footer.links.contact')}</Link> |
            <Link to="/privacy-policy">{t('footer.links.privacy')}</Link> |
            <Link to="/terms-of-service">{t('footer.links.terms')}</Link> |
            <a href="mailto:<EMAIL>">{t('footer.links.legal')}</a>
          </p>
        </Copyright>
      </FooterContent>
    </FooterContainer>
  );
};

export default Footer;
