import { createContext, useReducer, useEffect } from 'react';
// Removed complianceService - now using localStorage only

export const ComplianceContext = createContext();

const complianceReducer = (state, action) => {
  switch (action.type) {
    case 'SET_COOKIE_CONSENT':
      return {
        ...state,
        cookieConsent: action.payload
      };
    case 'SET_DISCLAIMERS':
      return {
        ...state,
        disclaimers: action.payload
      };
    case 'SET_PRIVACY_POLICY':
      return {
        ...state,
        privacyPolicy: action.payload
      };
    case 'SET_TERMS_OF_SERVICE':
      return {
        ...state,
        termsOfService: action.payload
      };
    case 'SET_LOADING':
      return {
        ...state,
        loading: action.payload
      };
    default:
      return state;
  }
};

const initialState = {
  cookieConsent: null,
  disclaimers: {
    riskWarning: null, // Will use translation
    affiliateDisclosure: null, // Will use translation
    countryRestriction: null // Will use translation
  },
  privacyPolicy: null,
  termsOfService: null,
  loading: false
};

export const ComplianceProvider = ({ children }) => {
  const [state, dispatch] = useReducer(complianceReducer, initialState);

  // Load compliance data on mount
  useEffect(() => {
    const loadComplianceData = async () => {
      try {
        // Load cookie consent status from localStorage
        const cookieConsent = JSON.parse(localStorage.getItem('cookieConsent') || '{}');
        dispatch({ type: 'SET_COOKIE_CONSENT', payload: cookieConsent });

      } catch (error) {
        console.error('Error loading compliance data:', error);
      }
    };

    loadComplianceData();
  }, []);

  const saveCookieConsent = async (preferences) => {
    try {
      // Save to localStorage with consent flag
      const consentData = {
        consent: preferences,
        timestamp: new Date().toISOString()
      };
      localStorage.setItem('cookieConsent', JSON.stringify(consentData));
      dispatch({ type: 'SET_COOKIE_CONSENT', payload: consentData });

      // Enable/disable analytics based on consent
      if (preferences.analytics) {
        // Enable Google Analytics
        window.gtag && window.gtag('consent', 'update', {
          analytics_storage: 'granted'
        });
      } else {
        // Disable Google Analytics
        window.gtag && window.gtag('consent', 'update', {
          analytics_storage: 'denied'
        });
      }

      return { success: true, preferences };
    } catch (error) {
      console.error('Error saving cookie consent:', error);
      throw error;
    }
  };

  const loadPrivacyPolicy = async () => {
    if (state.privacyPolicy) return state.privacyPolicy;

    try {
      // Static privacy policy content
      const privacyPolicy = {
        content: "Privacy Policy content would go here...",
        lastUpdated: new Date().toISOString()
      };
      dispatch({ type: 'SET_PRIVACY_POLICY', payload: privacyPolicy });
      return privacyPolicy;
    } catch (error) {
      console.error('Error loading privacy policy:', error);
      throw error;
    }
  };

  const loadTermsOfService = async () => {
    if (state.termsOfService) return state.termsOfService;

    try {
      // Static terms of service content
      const termsOfService = {
        content: "Terms of Service content would go here...",
        lastUpdated: new Date().toISOString()
      };
      dispatch({ type: 'SET_TERMS_OF_SERVICE', payload: termsOfService });
      return termsOfService;
    } catch (error) {
      console.error('Error loading terms of service:', error);
      throw error;
    }
  };

  const submitDataRequest = async (email, requestType) => {
    try {
      // Mock data request submission (no backend needed)
      console.log(`Data request submitted: ${requestType} for ${email}`);
      return { success: true, message: 'Data request submitted successfully' };
    } catch (error) {
      console.error('Error submitting data request:', error);
      throw error;
    }
  };

  // Helper functions to check compliance requirements
  const shouldShowCookieConsent = () => {
    return !state.cookieConsent || !state.cookieConsent.consent;
  };

  const hasAnalyticsConsent = () => {
    return state.cookieConsent?.consent?.analytics === true;
  };

  const hasMarketingConsent = () => {
    return state.cookieConsent?.consent?.marketing === true;
  };

  const getDisclaimer = (type) => {
    return state.disclaimers?.[type] || null;
  };

  const value = {
    ...state,
    saveCookieConsent,
    loadPrivacyPolicy,
    loadTermsOfService,
    submitDataRequest,
    shouldShowCookieConsent,
    hasAnalyticsConsent,
    hasMarketingConsent,
    getDisclaimer
  };

  return (
    <ComplianceContext.Provider value={value}>
      {children}
    </ComplianceContext.Provider>
  );
};


