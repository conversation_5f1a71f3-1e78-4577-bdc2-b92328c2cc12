import { createContext, useEffect } from 'react';
import { analyticsService } from '../services/analyticsService';

export const AnalyticsContext = createContext();

export const AnalyticsProvider = ({ children }) => {
  // Initialize analytics on app load
  useEffect(() => {
    analyticsService.initialize();
    
    // Track initial page view
    analyticsService.trackPageView(window.location.pathname);
    
    // Track session start
    analyticsService.trackEvent('session_start', window.location.pathname);
    
    // Track session end on page unload
    const handleBeforeUnload = () => {
      analyticsService.trackEvent('session_end', window.location.pathname);
    };
    
    window.addEventListener('beforeunload', handleBeforeUnload);
    
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, []);

  const trackPageView = (page, additionalData = {}) => {
    analyticsService.trackPageView(page, additionalData);
  };

  const trackEvent = (event, page, metadata = {}) => {
    analyticsService.trackEvent(event, page, metadata);
  };

  const trackButtonClick = (buttonName, page, additionalData = {}) => {
    analyticsService.trackEvent('button_click', page, {
      buttonName,
      ...additionalData
    });
  };

  const trackFormSubmit = (formName, page, additionalData = {}) => {
    analyticsService.trackEvent('form_submit', page, {
      formName,
      ...additionalData
    });
  };

  const trackVideoPlay = (videoId, page, additionalData = {}) => {
    analyticsService.trackEvent('video_play', page, {
      videoId,
      ...additionalData
    });
  };

  const trackVideoComplete = (videoId, page, additionalData = {}) => {
    analyticsService.trackEvent('video_complete', page, {
      videoId,
      ...additionalData
    });
  };

  const trackAffiliateClick = (linkId, page, additionalData = {}) => {
    analyticsService.trackEvent('affiliate_click', page, {
      linkId,
      ...additionalData
    });
  };

  const trackLeadCapture = (source, page, additionalData = {}) => {
    analyticsService.trackEvent('lead_capture', page, {
      source,
      ...additionalData
    });
  };

  const trackScrollDepth = (depth, page) => {
    analyticsService.trackEvent('scroll_depth', page, {
      depth: `${depth}%`
    });
  };

  const value = {
    trackPageView,
    trackEvent,
    trackButtonClick,
    trackFormSubmit,
    trackVideoPlay,
    trackVideoComplete,
    trackAffiliateClick,
    trackLeadCapture,
    trackScrollDepth
  };

  return (
    <AnalyticsContext.Provider value={value}>
      {children}
    </AnalyticsContext.Provider>
  );
};


