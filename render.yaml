services:
  - type: web
    name: secondstrike-copy-trading
    env: node
    buildCommand: npm run build
    startCommand: npm start
    envVars:
      - key: NODE_ENV
        value: production
      - key: VITE_APP_NAME
        value: SecondStrike
      - key: VITE_PRIMARY_AFFILIATE_URL
        value: https://u3.shortink.io/main?utm_campaign=54002&utm_source=affiliate&utm_medium=sr&a=GKRfdEpZZIFL1s&ac=turn&code=50START
      - key: VITE_SECONDARY_AFFILIATE_URL
        value: https://p.finance/en/?utm_campaign=54002&utm_source=affiliate&utm_medium=sr&a=GKRfdEpZZIFL1s&ac=turn&code=50START
      # Add these in Render dashboard (sensitive data):
      # - MONGODB_URI
      # - SESSION_SECRET
      # - FRONTEND_URL
