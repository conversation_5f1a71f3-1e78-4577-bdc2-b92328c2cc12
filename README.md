# SecondStrike - PocketOption Affiliate Funnel

A high-converting affiliate marketing funnel built with React to drive traffic to PocketOption. Now optimized as a **static application** for maximum performance and minimal deployment complexity.

## 🎯 Project Overview

This project creates a complete affiliate marketing funnel for Pocket Option with:
- **Landing pages** with hero videos and strategy showcases
- **Lead capture system** with chat widgets and autoresponders
- **Compliance framework** with all required disclaimers and GDPR support
- **Analytics tracking** with UTM parameters and conversion metrics
- **Authentication system** for protected onboarding areas
- **Performance dashboard** with trading statistics

## 🏗️ Architecture

```
├── backend/          # Express.js API server
│   ├── routes/       # API endpoints
│   ├── models/       # MongoDB schemas
│   ├── middleware/   # Authentication & validation
│   └── utils/        # Helper functions
├── frontend/         # React application
│   ├── src/
│   │   ├── components/  # Reusable UI components
│   │   ├── pages/       # Route components
│   │   ├── hooks/       # Custom React hooks
│   │   └── utils/       # Frontend utilities
└── package.json      # Root package manager
```

## 🚀 Quick Start

### Prerequisites
- Node.js 20.11.0 or higher
- MongoDB (local or cloud)
- Email service (Gmail/SMTP)

### Installation

1. **Clone and install dependencies:**
```bash
npm run install:all
```

2. **Set up environment variables:**
```bash
# Backend
cp backend/.env.example backend/.env
# Edit backend/.env with your configuration
```

3. **Start development servers:**
```bash
npm run dev
```

This will start:
- Backend API server on `http://localhost:5000`
- Frontend React app on `http://localhost:5173`

## 📋 Environment Configuration

### Backend (.env)
```env
# Server
PORT=5000
NODE_ENV=development
FRONTEND_URL=http://localhost:5173

# Database
MONGODB_URI=mongodb://localhost:27017/pocketoption-funnel

# Security
SESSION_SECRET=your-super-secret-session-key
JWT_SECRET=your-jwt-secret-key

# Email
EMAIL_HOST=smtp.gmail.com
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password

# Affiliate
AFFILIATE_LINK_BASE=https://po.trade/cabinet/demo-high-low/
AFFILIATE_ID=your-affiliate-id
STRATEGY_ID=123456

# Analytics
GOOGLE_ANALYTICS_ID=GA_MEASUREMENT_ID
```

## 🎨 Features

### ✅ Compliance & Legal
- **Country restrictions disclaimer** in footer
- **Affiliate disclosure** adjacent to all affiliate links
- **Risk warnings** inline with performance claims
- **GDPR cookie consent** with preference management
- **Privacy policy & Terms of Service** pages
- **Data request handling** for GDPR compliance

### 📊 Analytics & Tracking
- **Google Analytics integration** with cookie consent
- **UTM parameter tracking** for all traffic sources
- **Conversion funnel analysis** from visit to deposit
- **Lead scoring system** based on engagement
- **Affiliate link performance metrics**

### 🎯 Funnel Pages
1. **Landing Page** - Hero video, strategy showcase, main CTA
2. **Proof Page** - Testimonials, journal screenshots, social proof
3. **Pitch Page** - Consultative approach, objection handling
4. **Lead Capture** - Chat widgets, email forms, autoresponders
5. **Onboarding Hub** - Protected area with step-by-step guides
6. **Performance Dashboard** - Trading stats and updates

### 🔐 Authentication & Security
- **JWT-based authentication** for protected areas
- **Session management** with MongoDB store
- **Rate limiting** to prevent abuse
- **Helmet.js security headers**
- **Input validation** and sanitization

## 🛠️ Development

### Available Scripts

```bash
# Development
npm run dev                 # Start both frontend and backend
npm run backend:dev         # Backend only
npm run frontend:dev        # Frontend only

# Production
npm run build              # Build frontend for production
npm run start              # Start production server

# Utilities
npm run install:all        # Install all dependencies
```

### API Endpoints

#### Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `GET /api/auth/me` - Get current user

#### Lead Management
- `POST /api/leads/capture` - Capture new lead
- `POST /api/leads/ready-response` - Handle "READY" chat response
- `GET /api/leads/stats` - Lead statistics

#### Analytics
- `POST /api/analytics/track` - Track user events
- `GET /api/analytics/dashboard` - Analytics dashboard data
- `GET /api/analytics/conversions` - Conversion metrics

#### Compliance
- `GET /api/compliance/disclaimers` - Get legal disclaimers
- `GET /api/compliance/privacy-policy` - Privacy policy content
- `POST /api/compliance/cookie-consent` - Save cookie preferences

## 🎨 Styling & Branding

The frontend uses Pocket Option's visual identity:
- **Colors**: Blue (#667eea), White, Purple gradients
- **Typography**: Clean, professional fonts
- **Components**: Matching Pocket Option's UI patterns
- **Responsive**: Mobile-first design approach

## 📱 Mobile Optimization

- Responsive design for all screen sizes
- Touch-friendly interface elements
- Optimized loading for mobile networks
- Progressive Web App features

## 🔒 Security Features

- **HTTPS enforcement** in production
- **CORS configuration** for API security
- **Rate limiting** on all endpoints
- **Input validation** and sanitization
- **Secure session management**
- **Environment variable protection**

## 📈 Performance

- **Code splitting** for faster loading
- **Image optimization** and lazy loading
- **CDN-ready** static asset serving
- **Database indexing** for fast queries
- **Caching strategies** for API responses

## 🚀 Deployment

### Production Checklist
- [ ] Set `NODE_ENV=production`
- [ ] Configure production database
- [ ] Set up SSL certificates
- [ ] Configure email service
- [ ] Set up monitoring and logging
- [ ] Test all compliance features
- [ ] Verify affiliate link tracking

### Recommended Hosting
- **Backend**: Heroku, DigitalOcean, AWS
- **Database**: MongoDB Atlas
- **Frontend**: Vercel, Netlify
- **Email**: SendGrid, Mailgun

## 📞 Support

For questions about this implementation:
1. Check the inline code documentation
2. Review the API endpoint documentation
3. Test with the provided example data
4. Ensure all environment variables are set correctly

## ⚠️ Legal Compliance

This implementation includes all required compliance features:
- Risk warnings and disclaimers
- Affiliate relationship disclosure
- Country restriction notices
- GDPR data protection compliance
- Cookie consent management

**Important**: Always consult with legal counsel to ensure compliance with local regulations in your jurisdiction.
