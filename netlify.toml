[build]
  # Directory to change to before starting a build
  base = "frontend/"
  
  # Directory that contains the deploy-ready HTML files and assets
  publish = "dist/"
  
  # Default build command
  command = "npm run build"

[build.environment]
  # Node.js version
  NODE_VERSION = "18"

# SPA redirect rules (alternative to _redirects file)
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Security headers
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"

# Cache static assets
[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"
