// Utility functions for generating and managing affiliate links

const AFFILIATE_BASE_URL = process.env.AFFILIATE_LINK_BASE || 'https://po.trade/cabinet/demo-high-low/';
const AFFILIATE_ID = process.env.AFFILIATE_ID || 'your-affiliate-id';
const STRATEGY_ID = process.env.STRATEGY_ID || '123456';

/**
 * Generate a personalized affiliate link with UTM parameters
 * @param {Object} options - Options for link generation
 * @param {string} options.leadId - Lead ID for tracking
 * @param {string} options.source - Traffic source
 * @param {Object} options.utmParams - UTM parameters
 * @param {string} options.campaign - Campaign name
 * @returns {string} Generated affiliate link
 */
export const generateAffiliateLink = (options = {}) => {
  const {
    leadId,
    source = 'website',
    utmParams = {},
    campaign = 'copy-trading'
  } = options;

  const url = new URL(AFFILIATE_BASE_URL);
  
  // Add affiliate ID
  url.searchParams.set('affiliate_id', AFFILIATE_ID);
  
  // Add strategy ID for copy trading
  url.searchParams.set('strategy_id', STRATEGY_ID);
  
  // Add tracking parameters
  if (leadId) {
    url.searchParams.set('lead_id', leadId);
  }
  
  // Add UTM parameters for tracking
  const utmDefaults = {
    utm_source: utmParams.utm_source || source,
    utm_medium: utmParams.utm_medium || 'affiliate',
    utm_campaign: utmParams.utm_campaign || campaign,
    utm_content: utmParams.utm_content || 'registration_link'
  };
  
  Object.entries(utmDefaults).forEach(([key, value]) => {
    if (value) {
      url.searchParams.set(key, value);
    }
  });
  
  // Add timestamp for uniqueness
  url.searchParams.set('timestamp', Date.now().toString());
  
  return url.toString();
};

/**
 * Generate affiliate link for specific funnel page
 * @param {string} page - Page type (landing, proof, pitch, etc.)
 * @param {Object} options - Additional options
 * @returns {string} Generated affiliate link
 */
export const generatePageSpecificLink = (page, options = {}) => {
  const pageConfigs = {
    landing: {
      utm_content: 'hero_cta',
      utm_term: 'copy_trading_signup'
    },
    proof: {
      utm_content: 'proof_testimonial',
      utm_term: 'social_proof_conversion'
    },
    pitch: {
      utm_content: 'consultation_pitch',
      utm_term: 'scarcity_offer'
    },
    onboarding: {
      utm_content: 'onboarding_guide',
      utm_term: 'step_by_step'
    }
  };

  const pageConfig = pageConfigs[page] || {};
  
  return generateAffiliateLink({
    ...options,
    utmParams: {
      ...options.utmParams,
      ...pageConfig
    },
    campaign: `${page}_funnel`
  });
};

/**
 * Parse affiliate link to extract tracking information
 * @param {string} url - Affiliate URL to parse
 * @returns {Object} Parsed tracking data
 */
export const parseAffiliateLink = (url) => {
  try {
    const urlObj = new URL(url);
    const params = Object.fromEntries(urlObj.searchParams);
    
    return {
      affiliateId: params.affiliate_id,
      strategyId: params.strategy_id,
      leadId: params.lead_id,
      utmParams: {
        utm_source: params.utm_source,
        utm_medium: params.utm_medium,
        utm_campaign: params.utm_campaign,
        utm_term: params.utm_term,
        utm_content: params.utm_content
      },
      timestamp: params.timestamp
    };
  } catch (error) {
    console.error('Error parsing affiliate link:', error);
    return null;
  }
};

/**
 * Generate short URL for affiliate links (for social media, etc.)
 * @param {string} fullUrl - Full affiliate URL
 * @returns {string} Shortened URL (placeholder implementation)
 */
export const generateShortUrl = (fullUrl) => {
  // In a real implementation, you would integrate with a URL shortening service
  // like bit.ly, tinyurl, or create your own shortening service
  
  // For now, return a placeholder short URL
  const hash = Buffer.from(fullUrl).toString('base64').slice(0, 8);
  return `https://yoursite.com/go/${hash}`;
};

/**
 * Track affiliate link click
 * @param {string} linkId - Link identifier
 * @param {Object} clickData - Click tracking data
 */
export const trackAffiliateClick = async (linkId, clickData) => {
  try {
    // Import here to avoid circular dependencies
    const Analytics = (await import('../models/Analytics.js')).default;
    
    const analyticsEntry = new Analytics({
      event: 'affiliate_click',
      page: clickData.page || 'unknown',
      sessionId: clickData.sessionId,
      utmParams: clickData.utmParams || {},
      userAgent: clickData.userAgent,
      ipAddress: clickData.ipAddress,
      metadata: {
        linkId,
        affiliateId: AFFILIATE_ID,
        strategyId: STRATEGY_ID,
        ...clickData.metadata
      }
    });
    
    await analyticsEntry.save();
    console.log('Affiliate click tracked:', linkId);
  } catch (error) {
    console.error('Error tracking affiliate click:', error);
  }
};

/**
 * Get affiliate performance metrics
 * @param {Object} filters - Filters for metrics
 * @returns {Object} Performance metrics
 */
export const getAffiliateMetrics = async (filters = {}) => {
  try {
    const Analytics = (await import('../models/Analytics.js')).default;
    const Lead = (await import('../models/Lead.js')).default;
    
    const dateFilter = {};
    if (filters.startDate && filters.endDate) {
      dateFilter.timestamp = {
        $gte: new Date(filters.startDate),
        $lte: new Date(filters.endDate)
      };
    }
    
    // Get click metrics
    const clicks = await Analytics.countDocuments({
      event: 'affiliate_click',
      ...dateFilter
    });
    
    // Get conversion metrics
    const conversions = await Lead.countDocuments({
      'affiliateLinks.registered': true,
      ...dateFilter
    });
    
    const deposits = await Lead.countDocuments({
      'affiliateLinks.deposited': true,
      ...dateFilter
    });
    
    return {
      clicks,
      conversions,
      deposits,
      clickToRegistrationRate: clicks > 0 ? (conversions / clicks * 100).toFixed(2) : 0,
      registrationToDepositRate: conversions > 0 ? (deposits / conversions * 100).toFixed(2) : 0
    };
  } catch (error) {
    console.error('Error getting affiliate metrics:', error);
    return null;
  }
};
