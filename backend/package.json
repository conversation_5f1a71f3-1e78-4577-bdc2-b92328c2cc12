{"name": "pocketoption-backend", "version": "1.0.0", "description": "Backend for Pocket Option affiliate funnel site", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "express-rate-limit": "^8.0.1", "helmet": "^8.1.0", "morgan": "^1.10.1"}, "devDependencies": {"concurrently": "^9.2.0", "nodemon": "^3.1.10"}}