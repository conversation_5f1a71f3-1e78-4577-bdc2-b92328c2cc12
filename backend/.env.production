# Production Environment Variables for Render.com
# Copy these to your Render.com environment variables section

# Server Configuration
NODE_ENV=production
PORT=10000

# Database
MONGODB_URI=your_mongodb_connection_string_here

# Session Security
SESSION_SECRET=your_super_secure_session_secret_here

# Frontend URL (your Render.com domain)
FRONTEND_URL=https://your-app-name.onrender.com

# Affiliate URLs (already configured)
VITE_PRIMARY_AFFILIATE_URL=https://u3.shortink.io/main?utm_campaign=54002&utm_source=affiliate&utm_medium=sr&a=GKRfdEpZZIFL1s&ac=turn&code=50START
VITE_SECONDARY_AFFILIATE_URL=https://p.finance/en/?utm_campaign=54002&utm_source=affiliate&utm_medium=sr&a=GKRfdEpZZIFL1s&ac=turn&code=50START

# App Configuration
VITE_APP_NAME=SecondStrike
VITE_APP_DESCRIPTION=Professional copy trading service for PocketOption

# Analytics (if using Google Analytics)
VITE_GA_TRACKING_ID=your_ga_tracking_id_here

# Email Configuration (if needed for server-side email)
SMTP_HOST=smtp.protonmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_protonmail_app_password_here

# Security
CORS_ORIGIN=https://your-app-name.onrender.com
